# OpenBao Configuration Template
# This template uses environment variables for dynamic configuration

# Basic configuration
ui = true
disable_mlock = true

# API listener
listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = true
}

# Cluster listener  
listener "tcp" {
  address         = "0.0.0.0:8201"
  tls_disable     = true
  cluster_address = "0.0.0.0:8201"
}

# Storage backend - Raft with dynamic configuration
storage "raft" {
  path    = "${OPENBAO_RAFT_PATH}"
  node_id = "${OPENBAO_RAFT_NODE_ID}"
  
  # Join configuration - all nodes try to join node-1
  retry_join {
    leader_api_addr = "http://openbao-node-1:8200"
  }
  
  # Conditional non-voter configuration
  %{ if OPENBAO_NODE_TYPE == "non-voter" ~}
  # This node will not participate in leader election
  non_voter = true
  %{ endif ~}
}

# Cluster configuration using environment variables
cluster_addr = "${OPENBAO_CLUSTER_ADDR}"
api_addr     = "${OPENBAO_API_ADDR}"

# Logging configuration
log_level = "${OPENBAO_LOG_LEVEL}"
log_format = "${OPENBAO_LOG_FORMAT}"

# Telemetry configuration
telemetry {
  prometheus_retention_time = "${OPENBAO_TELEMETRY_RETENTION}"
  disable_hostname = ${OPENBAO_TELEMETRY_DISABLE_HOSTNAME}
  
  %{ if OPENBAO_TELEMETRY_STATSD_ADDRESS != "" ~}
  statsd_address = "${OPENBAO_TELEMETRY_STATSD_ADDRESS}"
  %{ endif ~}
  
  %{ if OPENBAO_TELEMETRY_STATSITE_ADDRESS != "" ~}
  statsite_address = "${OPENBAO_TELEMETRY_STATSITE_ADDRESS}"
  %{ endif ~}
}

# Conditional seal configuration for auto-unseal
%{ if OPENBAO_SEAL_TYPE != "" ~}
seal "${OPENBAO_SEAL_TYPE}" {
  %{ if OPENBAO_SEAL_TYPE == "awskms" ~}
  region     = "${OPENBAO_SEAL_AWS_REGION}"
  kms_key_id = "${OPENBAO_SEAL_AWS_KMS_KEY_ID}"
  %{ endif ~}
  
  %{ if OPENBAO_SEAL_TYPE == "gcpckms" ~}
  project    = "${OPENBAO_SEAL_GCP_PROJECT}"
  region     = "${OPENBAO_SEAL_GCP_REGION}"
  key_ring   = "${OPENBAO_SEAL_GCP_KEY_RING}"
  crypto_key = "${OPENBAO_SEAL_GCP_CRYPTO_KEY}"
  %{ endif ~}
  
  %{ if OPENBAO_SEAL_TYPE == "azurekeyvault" ~}
  tenant_id     = "${OPENBAO_SEAL_AZURE_TENANT_ID}"
  client_id     = "${OPENBAO_SEAL_AZURE_CLIENT_ID}"
  client_secret = "${OPENBAO_SEAL_AZURE_CLIENT_SECRET}"
  vault_name    = "${OPENBAO_SEAL_AZURE_VAULT_NAME}"
  key_name      = "${OPENBAO_SEAL_AZURE_KEY_NAME}"
  %{ endif ~}
}
%{ endif ~}

# Performance and cache configuration
%{ if OPENBAO_CACHE_SIZE != "" ~}
cache {
  size = "${OPENBAO_CACHE_SIZE}"
}
%{ endif ~}

# High availability configuration
%{ if OPENBAO_HA_ENABLED == "true" ~}
ha_storage "raft" {
  path    = "${OPENBAO_RAFT_PATH}"
  node_id = "${OPENBAO_RAFT_NODE_ID}"
}
%{ endif ~}

# Plugin directory
%{ if OPENBAO_PLUGIN_DIRECTORY != "" ~}
plugin_directory = "${OPENBAO_PLUGIN_DIRECTORY}"
%{ endif ~}

# Additional listeners for specific use cases
%{ if OPENBAO_METRICS_LISTENER_ENABLED == "true" ~}
listener "tcp" {
  address         = "0.0.0.0:${OPENBAO_METRICS_PORT}"
  tls_disable     = true
  purpose         = ["metrics"]
}
%{ endif ~}

# Enterprise features (if available)
%{ if OPENBAO_LICENSE_PATH != "" ~}
license_path = "${OPENBAO_LICENSE_PATH}"
%{ endif ~}

# Entropy configuration
%{ if OPENBAO_ENTROPY_AUGMENTATION == "true" ~}
entropy "seal" {
  mode = "augmentation"
}
%{ endif ~}
