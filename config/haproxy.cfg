global
    daemon
    log stdout local0 info
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy

    # Default SSL material locations
    ca-base /etc/ssl/certs
    crt-base /etc/ssl/private

    # Intermediate configuration
    ssl-default-bind-ciphers ECDHE+AESGCM:ECDHE+CHACHA20:RSA+AESGCM:RSA+SHA256
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option log-health-checks
    option forwardfor
    option http-server-close
    timeout connect 5000
    timeout client  50000
    timeout server  50000
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

# HAProxy Statistics
frontend stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    stats show-legends
    stats show-desc OpenBao Cluster Load Balancer

# OpenBao API Frontend
frontend openbao_api
    bind *:8200
    mode http

    # Health check endpoint - always allow
    acl health_check path_beg /v1/sys/health

    # Capture original client IP
    option forwardfor

    # Add custom headers for debugging
    http-request set-header X-Forwarded-Proto http
    http-request set-header X-Load-Balancer HAProxy

    # Route to backend
    default_backend openbao_nodes

# OpenBao Backend - All nodes for read operations
backend openbao_nodes
    mode http
    balance roundrobin

    # Health check configuration
    option httpchk GET /v1/sys/health
    http-check expect status 200,429,472,473,501,503

    # Server definitions
    server openbao-node-1 openbao-node-1:8200 check inter 10s fall 3 rise 2
    server openbao-node-2 openbao-node-2:8200 check inter 10s fall 3 rise 2
    server openbao-node-3 openbao-node-3:8200 check inter 10s fall 3 rise 2
    server openbao-node-4 openbao-node-4:8200 check inter 10s fall 3 rise 2
    server openbao-node-5 openbao-node-5:8200 check inter 10s fall 3 rise 2

    # Retry configuration
    retries 3
    option redispatch

    # Connection settings
    timeout connect 5s
    timeout server 30s

    # Add server info to response headers (for debugging)
    http-response set-header X-Server %s

# Alternative backend for write operations (active node only)
# This can be used for applications that need to ensure writes go to the leader
backend openbao_leader
    mode http
    balance first

    # More strict health check for leader
    option httpchk GET /v1/sys/leader
    http-check expect status 200

    # Only route to nodes that are confirmed leaders/active
    server openbao-node-1 openbao-node-1:8200 check inter 5s fall 2 rise 1
    server openbao-node-2 openbao-node-2:8200 check inter 5s fall 2 rise 1 backup
    server openbao-node-3 openbao-node-3:8200 check inter 5s fall 2 rise 1 backup

    # Faster timeouts for write operations
    timeout connect 3s
    timeout server 15s
