# OpenBao configuration for Node 1 (Voter)
ui = true
disable_mlock = true

# API listener
listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = true
}

# Cluster listener
listener "tcp" {
  address         = "0.0.0.0:8201"
  tls_disable     = true
  cluster_address = "0.0.0.0:8201"
}

# Storage backend - Raft
storage "raft" {
  path    = "/openbao/data"
  node_id = "node-1"
  
  # This is the first node, so it will be the initial leader
  retry_join {
    leader_api_addr = "http://openbao-node-1:8200"
  }
}

# Cluster configuration
cluster_addr = "https://openbao-node-1:8201"
api_addr     = "http://openbao-node-1:8200"

# Logging
log_level = "INFO"
log_format = "standard"

# Telemetry (optional)
telemetry {
  prometheus_retention_time = "30s"
  disable_hostname = true
}
