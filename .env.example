# OpenBao Cluster Configuration
# Copy this file to .env and customize as needed

# Basic Configuration
OPENBAO_LOG_LEVEL=INFO
OPENBAO_LOG_FORMAT=standard

# Telemetry Configuration
OPENBAO_TELEMETRY_RETENTION=30s
OPENBAO_TELEMETRY_DISABLE_HOSTNAME=true
OPENBAO_TELEMETRY_STATSD_ADDRESS=
OPENBAO_TELEMETRY_STATSITE_ADDRESS=

# Auto-Unseal Configuration (Optional)
# Uncomment and configure for auto-unseal
# OPENBAO_SEAL_TYPE=awskms
# OPENBAO_SEAL_AWS_REGION=us-west-2
# OPENBAO_SEAL_AWS_KMS_KEY_ID=your-kms-key-id

# GCP KMS Auto-Unseal
# OPENBAO_SEAL_TYPE=gcpckms
# OPENBAO_SEAL_GCP_PROJECT=your-project
# OPENBAO_SEAL_GCP_REGION=global
# OPENBAO_SEAL_GCP_KEY_RING=your-key-ring
# OPENBAO_SEAL_GCP_CRYPTO_KEY=your-crypto-key

# Azure Key Vault Auto-Unseal
# OPENBAO_SEAL_TYPE=azurekeyvault
# OPENBAO_SEAL_AZURE_TENANT_ID=your-tenant-id
# OPENBAO_SEAL_AZURE_CLIENT_ID=your-client-id
# OPENBAO_SEAL_AZURE_CLIENT_SECRET=your-client-secret
# OPENBAO_SEAL_AZURE_VAULT_NAME=your-vault-name
# OPENBAO_SEAL_AZURE_KEY_NAME=your-key-name

# Performance Configuration
OPENBAO_CACHE_SIZE=
OPENBAO_HA_ENABLED=false

# Plugin Configuration
OPENBAO_PLUGIN_DIRECTORY=

# Metrics Configuration
OPENBAO_METRICS_LISTENER_ENABLED=false
OPENBAO_METRICS_PORT=8202

# Enterprise Configuration (if applicable)
OPENBAO_LICENSE_PATH=

# Security Configuration
OPENBAO_ENTROPY_AUGMENTATION=false

# Cluster Scaling Configuration
# Set to true to enable dynamic scaling
OPENBAO_DYNAMIC_SCALING=false
OPENBAO_MIN_NODES=3
OPENBAO_MAX_NODES=10

# Load Balancer Configuration
HAPROXY_STATS_ENABLED=true
HAPROXY_STATS_PORT=8404
HAPROXY_STATS_USER=admin
HAPROXY_STATS_PASSWORD=admin

# Network Configuration
OPENBAO_NETWORK_SUBNET=**********/16
OPENBAO_API_PORT=8200
OPENBAO_CLUSTER_PORT=8201

# Development/Testing Configuration
OPENBAO_DEV_MODE=false
OPENBAO_DEV_ROOT_TOKEN_ID=
OPENBAO_DEV_LISTEN_ADDRESS=0.0.0.0:8200
