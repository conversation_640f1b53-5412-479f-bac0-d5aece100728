#!/bin/bash

# Test script to demonstrate load balancer functionality

set -e

LB_URL="http://localhost:8200"
LB_STATS_URL="http://localhost:8404/stats"

echo "OpenBao Load Balancer Test Script"
echo "================================="
echo ""

function test_connectivity() {
    echo "Testing connectivity to load balancer..."
    
    if curl -s $LB_URL/v1/sys/health > /dev/null; then
        echo "✅ Load balancer is accessible"
        
        # Get cluster status
        status=$(curl -s $LB_URL/v1/sys/health | jq -r '.initialized // false')
        sealed=$(curl -s $LB_URL/v1/sys/health | jq -r '.sealed // true')
        
        echo "   - Initialized: $status"
        echo "   - Sealed: $sealed"
    else
        echo "❌ Load balancer is not accessible"
        return 1
    fi
}

function test_load_distribution() {
    echo ""
    echo "Testing load distribution..."
    echo "Making 10 requests to see which nodes respond:"
    echo ""
    
    for i in {1..10}; do
        # Make request and capture the X-Server header that HAProxy adds
        server=$(curl -s -I $LB_URL/v1/sys/health | grep -i "x-server" | cut -d' ' -f2 | tr -d '\r')
        if [ -n "$server" ]; then
            echo "Request $i: Served by $server"
        else
            echo "Request $i: No server header (may not be configured)"
        fi
        sleep 0.5
    done
}

function show_haproxy_stats() {
    echo ""
    echo "HAProxy Statistics:"
    echo "=================="
    echo "Full stats available at: $LB_STATS_URL"
    echo ""
    
    # Try to get basic stats
    if command -v lynx > /dev/null; then
        echo "Backend status (using lynx):"
        lynx -dump $LB_STATS_URL | grep -A 10 "openbao_nodes" || echo "Could not parse stats"
    else
        echo "Install 'lynx' for formatted stats display, or visit: $LB_STATS_URL"
        echo ""
        echo "Raw backend status:"
        curl -s $LB_STATS_URL | grep -o "openbao-node-[0-9].*UP\|openbao-node-[0-9].*DOWN" || echo "Could not fetch backend status"
    fi
}

function test_failover() {
    echo ""
    echo "Testing failover capability..."
    echo "This will stop one node and test if requests still work"
    echo ""
    
    read -p "Do you want to test failover? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Stopping openbao-node-2 for 30 seconds..."
        docker stop openbao-node-2
        
        echo "Making requests while node-2 is down:"
        for i in {1..5}; do
            if curl -s $LB_URL/v1/sys/health > /dev/null; then
                echo "✅ Request $i: Success"
            else
                echo "❌ Request $i: Failed"
            fi
            sleep 2
        done
        
        echo "Restarting openbao-node-2..."
        docker start openbao-node-2
        
        echo "Waiting for node to rejoin..."
        sleep 10
        
        echo "Testing requests after node recovery:"
        for i in {1..3}; do
            if curl -s $LB_URL/v1/sys/health > /dev/null; then
                echo "✅ Request $i: Success"
            else
                echo "❌ Request $i: Failed"
            fi
            sleep 1
        done
    else
        echo "Skipping failover test"
    fi
}

function performance_test() {
    echo ""
    echo "Basic performance test..."
    echo "Making 50 concurrent requests:"
    
    start_time=$(date +%s)
    
    # Make 50 requests in parallel
    for i in {1..50}; do
        curl -s $LB_URL/v1/sys/health > /dev/null &
    done
    
    # Wait for all requests to complete
    wait
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo "✅ Completed 50 requests in ${duration} seconds"
    echo "   Average: $(echo "scale=2; 50 / $duration" | bc -l) requests/second"
}

function main() {
    echo "Starting load balancer tests..."
    echo ""
    
    # Check if cluster is running
    if ! docker ps | grep -q openbao-lb; then
        echo "❌ OpenBao cluster is not running. Start it with:"
        echo "   docker-compose up -d"
        exit 1
    fi
    
    test_connectivity
    test_load_distribution
    show_haproxy_stats
    test_failover
    
    if command -v bc > /dev/null; then
        performance_test
    else
        echo ""
        echo "Install 'bc' for performance testing"
    fi
    
    echo ""
    echo "Load balancer testing complete!"
    echo ""
    echo "Useful URLs:"
    echo "- OpenBao API: $LB_URL"
    echo "- HAProxy Stats: $LB_STATS_URL"
    echo "- Health Check: $LB_URL/v1/sys/health"
}

# Run main function
main
