#!/bin/bash

# OpenBao Cluster Management Script with Load Balancer

set -e

NODES=("openbao-node-1" "openbao-node-2" "openbao-node-3" "openbao-node-4" "openbao-node-5")
LB_URL="http://localhost:8200"
LB_STATS_URL="http://localhost:8404/stats"

function print_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       - Start the cluster with load balancer"
    echo "  stop        - Stop the cluster"
    echo "  status      - Show cluster status"
    echo "  init        - Initialize the cluster (run once)"
    echo "  unseal      - Unseal all nodes (requires unseal keys)"
    echo "  health      - Check health of all nodes"
    echo "  peers       - List Raft peers"
    echo "  logs        - Show logs for all nodes"
    echo "  lb-stats    - Show load balancer statistics"
    echo "  clean       - Stop and remove all data (WARNING: destructive)"
}

function start_cluster() {
    echo "Starting OpenBao cluster with load balancer..."
    docker-compose up -d
    echo "Waiting for nodes and load balancer to start..."
    sleep 15

    echo "Load balancer stats available at: $LB_STATS_URL"
    echo "OpenBao API available at: $LB_URL"
    echo ""
    check_health
}

function stop_cluster() {
    echo "Stopping OpenBao cluster..."
    docker-compose down
}

function cluster_status() {
    echo "Cluster Status:"
    docker-compose ps
}

function init_cluster() {
    echo "Initializing OpenBao cluster..."
    echo "This will initialize the cluster through the load balancer."

    # Wait for load balancer to be ready
    echo "Waiting for load balancer to be ready..."
    until curl -s $LB_URL/v1/sys/health > /dev/null 2>&1; do
        echo "Waiting for load balancer..."
        sleep 2
    done

    echo "Initializing through load balancer..."
    docker exec -it openbao-node-1 openbao operator init

    echo ""
    echo "IMPORTANT: Save the unseal keys and root token!"
    echo "You'll need the unseal keys to unseal all nodes."
    echo "Access the cluster through: $LB_URL"
}

function unseal_nodes() {
    echo "Unsealing all nodes..."
    echo "You'll need to provide 3 unseal keys for each node."

    for node in "${NODES[@]}"; do
        echo ""
        echo "Unsealing $node..."

        # Check if node is sealed by connecting directly to the container
        sealed=$(docker exec $node wget -qO- http://localhost:8200/v1/sys/seal-status 2>/dev/null | jq -r '.sealed' 2>/dev/null || echo "true")

        if [ "$sealed" = "true" ]; then
            echo "Node $node is sealed. Please provide 3 unseal keys:"
            for j in {1..3}; do
                echo -n "Unseal key $j: "
                read -s unseal_key
                echo ""
                docker exec -i $node openbao operator unseal "$unseal_key"
            done
        else
            echo "Node $node is already unsealed."
        fi
    done

    echo ""
    echo "All nodes processed. Check cluster status through load balancer:"
    echo "curl $LB_URL/v1/sys/health"
}

function check_health() {
    echo "Checking health through load balancer and individual nodes..."
    echo ""

    echo "=== Load Balancer Health ==="
    echo "Load Balancer URL: $LB_URL"
    health=$(curl -s $LB_URL/v1/sys/health 2>/dev/null || echo '{"errors":["unreachable"]}')
    echo "$health" | jq .
    echo ""

    echo "=== Individual Node Health ==="
    for node in "${NODES[@]}"; do
        echo "Node $node:"
        # Connect directly to container
        health=$(docker exec $node wget -qO- http://localhost:8200/v1/sys/health 2>/dev/null || echo '{"errors":["unreachable"]}')
        echo "$health" | jq .
        echo ""
    done

    echo "=== Load Balancer Stats ==="
    echo "HAProxy stats available at: $LB_STATS_URL"
}

function list_peers() {
    echo "Listing Raft peers..."
    docker exec -it openbao-node-1 openbao operator raft list-peers
}

function show_logs() {
    echo "Showing logs for all services..."
    docker-compose logs --tail=50
}

function show_lb_stats() {
    echo "Opening load balancer statistics..."
    echo "HAProxy Stats URL: $LB_STATS_URL"
    echo ""
    echo "You can also view detailed stats by visiting: $LB_STATS_URL"
    echo ""
    echo "Current backend status:"
    curl -s $LB_STATS_URL | grep -A 20 "openbao_nodes" || echo "Unable to fetch stats"
}

function clean_cluster() {
    echo "WARNING: This will stop the cluster and remove all data!"
    echo -n "Are you sure? (y/N): "
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "Stopping cluster and removing volumes..."
        docker-compose down -v
        echo "Cluster cleaned."
    else
        echo "Operation cancelled."
    fi
}

# Main script logic
case "${1:-}" in
    start)
        start_cluster
        ;;
    stop)
        stop_cluster
        ;;
    status)
        cluster_status
        ;;
    init)
        init_cluster
        ;;
    unseal)
        unseal_nodes
        ;;
    health)
        check_health
        ;;
    peers)
        list_peers
        ;;
    logs)
        show_logs
        ;;
    lb-stats)
        show_lb_stats
        ;;
    clean)
        clean_cluster
        ;;
    *)
        print_usage
        exit 1
        ;;
esac
