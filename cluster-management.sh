#!/bin/bash

# OpenBao Cluster Management Script

set -e

NODES=("openbao-node-1" "openbao-node-2" "openbao-node-3" "openbao-node-4" "openbao-node-5")
PORTS=(8200 8210 8220 8230 8240)

function print_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       - Start the cluster"
    echo "  stop        - Stop the cluster"
    echo "  status      - Show cluster status"
    echo "  init        - Initialize the cluster (run once)"
    echo "  unseal      - Unseal all nodes (requires unseal keys)"
    echo "  health      - Check health of all nodes"
    echo "  peers       - List Raft peers"
    echo "  logs        - Show logs for all nodes"
    echo "  clean       - Stop and remove all data (WARNING: destructive)"
}

function start_cluster() {
    echo "Starting OpenBao cluster..."
    docker-compose up -d
    echo "Waiting for nodes to start..."
    sleep 10
    check_health
}

function stop_cluster() {
    echo "Stopping OpenBao cluster..."
    docker-compose down
}

function cluster_status() {
    echo "Cluster Status:"
    docker-compose ps
}

function init_cluster() {
    echo "Initializing OpenBao cluster..."
    echo "This will initialize node-1 as the leader."
    
    # Wait for node-1 to be ready
    echo "Waiting for node-1 to be ready..."
    until curl -s http://localhost:8200/v1/sys/health > /dev/null 2>&1; do
        echo "Waiting for node-1..."
        sleep 2
    done
    
    echo "Initializing..."
    docker exec -it openbao-node-1 openbao operator init
    
    echo ""
    echo "IMPORTANT: Save the unseal keys and root token!"
    echo "You'll need the unseal keys to unseal all nodes."
}

function unseal_nodes() {
    echo "Unsealing all nodes..."
    echo "You'll need to provide 3 unseal keys for each node."
    
    for i in "${!NODES[@]}"; do
        node="${NODES[$i]}"
        port="${PORTS[$i]}"
        
        echo ""
        echo "Unsealing $node (port $port)..."
        
        # Check if node is sealed
        sealed=$(curl -s http://localhost:$port/v1/sys/seal-status | jq -r '.sealed' 2>/dev/null || echo "true")
        
        if [ "$sealed" = "true" ]; then
            echo "Node $node is sealed. Please provide 3 unseal keys:"
            for j in {1..3}; do
                echo -n "Unseal key $j: "
                read -s unseal_key
                echo ""
                docker exec -i $node openbao operator unseal "$unseal_key"
            done
        else
            echo "Node $node is already unsealed."
        fi
    done
}

function check_health() {
    echo "Checking health of all nodes..."
    echo ""
    
    for i in "${!NODES[@]}"; do
        node="${NODES[$i]}"
        port="${PORTS[$i]}"
        
        echo "Node $node (port $port):"
        health=$(curl -s http://localhost:$port/v1/sys/health 2>/dev/null || echo '{"errors":["unreachable"]}')
        echo "$health" | jq .
        echo ""
    done
}

function list_peers() {
    echo "Listing Raft peers..."
    docker exec -it openbao-node-1 openbao operator raft list-peers
}

function show_logs() {
    echo "Showing logs for all nodes..."
    docker-compose logs --tail=50
}

function clean_cluster() {
    echo "WARNING: This will stop the cluster and remove all data!"
    echo -n "Are you sure? (y/N): "
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "Stopping cluster and removing volumes..."
        docker-compose down -v
        echo "Cluster cleaned."
    else
        echo "Operation cancelled."
    fi
}

# Main script logic
case "${1:-}" in
    start)
        start_cluster
        ;;
    stop)
        stop_cluster
        ;;
    status)
        cluster_status
        ;;
    init)
        init_cluster
        ;;
    unseal)
        unseal_nodes
        ;;
    health)
        check_health
        ;;
    peers)
        list_peers
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_cluster
        ;;
    *)
        print_usage
        exit 1
        ;;
esac
