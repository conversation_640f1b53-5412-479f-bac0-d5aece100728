#!/bin/bash

# OpenBao Configuration Renderer
# This script renders the HCL template with environment variables

set -e

# Default values for optional environment variables
export OPENBAO_LOG_LEVEL="${OPENBAO_LOG_LEVEL:-INFO}"
export OPENBAO_LOG_FORMAT="${OPENBAO_LOG_FORMAT:-standard}"
export OPENBAO_TELEMETRY_RETENTION="${OPENBAO_TELEMETRY_RETENTION:-30s}"
export OPENBAO_TELEMETRY_DISABLE_HOSTNAME="${OPENBAO_TELEMETRY_DISABLE_HOSTNAME:-true}"
export OPENBAO_TELEMETRY_STATSD_ADDRESS="${OPENBAO_TELEMETRY_STATSD_ADDRESS:-}"
export OPENBAO_TELEMETRY_STATSITE_ADDRESS="${OPENBAO_TELEMETRY_STATSITE_ADDRESS:-}"
export OPENBAO_SEAL_TYPE="${OPENBAO_SEAL_TYPE:-}"
export OPENBAO_SEAL_AWS_REGION="${OPENBAO_SEAL_AWS_REGION:-}"
export OPENBAO_SEAL_AWS_KMS_KEY_ID="${OPENBAO_SEAL_AWS_KMS_KEY_ID:-}"
export OPENBAO_SEAL_GCP_PROJECT="${OPENBAO_SEAL_GCP_PROJECT:-}"
export OPENBAO_SEAL_GCP_REGION="${OPENBAO_SEAL_GCP_REGION:-}"
export OPENBAO_SEAL_GCP_KEY_RING="${OPENBAO_SEAL_GCP_KEY_RING:-}"
export OPENBAO_SEAL_GCP_CRYPTO_KEY="${OPENBAO_SEAL_GCP_CRYPTO_KEY:-}"
export OPENBAO_SEAL_AZURE_TENANT_ID="${OPENBAO_SEAL_AZURE_TENANT_ID:-}"
export OPENBAO_SEAL_AZURE_CLIENT_ID="${OPENBAO_SEAL_AZURE_CLIENT_ID:-}"
export OPENBAO_SEAL_AZURE_CLIENT_SECRET="${OPENBAO_SEAL_AZURE_CLIENT_SECRET:-}"
export OPENBAO_SEAL_AZURE_VAULT_NAME="${OPENBAO_SEAL_AZURE_VAULT_NAME:-}"
export OPENBAO_SEAL_AZURE_KEY_NAME="${OPENBAO_SEAL_AZURE_KEY_NAME:-}"
export OPENBAO_CACHE_SIZE="${OPENBAO_CACHE_SIZE:-}"
export OPENBAO_HA_ENABLED="${OPENBAO_HA_ENABLED:-false}"
export OPENBAO_PLUGIN_DIRECTORY="${OPENBAO_PLUGIN_DIRECTORY:-}"
export OPENBAO_METRICS_LISTENER_ENABLED="${OPENBAO_METRICS_LISTENER_ENABLED:-false}"
export OPENBAO_METRICS_PORT="${OPENBAO_METRICS_PORT:-8202}"
export OPENBAO_LICENSE_PATH="${OPENBAO_LICENSE_PATH:-}"
export OPENBAO_ENTROPY_AUGMENTATION="${OPENBAO_ENTROPY_AUGMENTATION:-false}"

# Function to render template using envsubst with conditional logic
render_template() {
    local template_file="$1"
    local output_file="$2"
    
    echo "Rendering OpenBao configuration for node: ${OPENBAO_RAFT_NODE_ID}"
    echo "Node type: ${OPENBAO_NODE_TYPE}"
    
    # Simple template rendering using envsubst for basic substitution
    # For more complex templating, we'd need a proper template engine
    
    # Create a temporary file with processed conditionals
    local temp_file="/tmp/openbao_config_temp.hcl"
    
    # Process the template
    cat "$template_file" > "$temp_file"
    
    # Handle conditional blocks for non-voter
    if [ "$OPENBAO_NODE_TYPE" = "non-voter" ]; then
        # Enable non-voter block
        sed -i 's/%{ if OPENBAO_NODE_TYPE == "non-voter" ~}//' "$temp_file"
        sed -i 's/%{ endif ~}//' "$temp_file"
    else
        # Remove non-voter block
        sed -i '/%{ if OPENBAO_NODE_TYPE == "non-voter" ~}/,/%{ endif ~}/d' "$temp_file"
    fi
    
    # Handle seal configuration
    if [ -n "$OPENBAO_SEAL_TYPE" ]; then
        sed -i 's/%{ if OPENBAO_SEAL_TYPE != "" ~}//' "$temp_file"
        
        # Handle specific seal types
        case "$OPENBAO_SEAL_TYPE" in
            "awskms")
                sed -i 's/%{ if OPENBAO_SEAL_TYPE == "awskms" ~}//' "$temp_file"
                sed -i '/%{ if OPENBAO_SEAL_TYPE == "gcpckms" ~}/,/%{ endif ~}/d' "$temp_file"
                sed -i '/%{ if OPENBAO_SEAL_TYPE == "azurekeyvault" ~}/,/%{ endif ~}/d' "$temp_file"
                ;;
            "gcpckms")
                sed -i 's/%{ if OPENBAO_SEAL_TYPE == "gcpckms" ~}//' "$temp_file"
                sed -i '/%{ if OPENBAO_SEAL_TYPE == "awskms" ~}/,/%{ endif ~}/d' "$temp_file"
                sed -i '/%{ if OPENBAO_SEAL_TYPE == "azurekeyvault" ~}/,/%{ endif ~}/d' "$temp_file"
                ;;
            "azurekeyvault")
                sed -i 's/%{ if OPENBAO_SEAL_TYPE == "azurekeyvault" ~}//' "$temp_file"
                sed -i '/%{ if OPENBAO_SEAL_TYPE == "awskms" ~}/,/%{ endif ~}/d' "$temp_file"
                sed -i '/%{ if OPENBAO_SEAL_TYPE == "gcpckms" ~}/,/%{ endif ~}/d' "$temp_file"
                ;;
        esac
        
        # Remove remaining endif markers for seal type
        sed -i 's/%{ endif ~}//' "$temp_file"
    else
        # Remove entire seal block
        sed -i '/%{ if OPENBAO_SEAL_TYPE != "" ~}/,/%{ endif ~}/d' "$temp_file"
    fi
    
    # Handle other conditional blocks
    handle_conditional_block() {
        local condition="$1"
        local env_var="$2"
        local env_value="$3"
        
        if [ "$env_value" = "true" ] || [ -n "$env_value" ]; then
            sed -i "s/%{ if $condition ~}//" "$temp_file"
            sed -i 's/%{ endif ~}//' "$temp_file"
        else
            sed -i "/%{ if $condition ~}/,/%{ endif ~}/d" "$temp_file"
        fi
    }
    
    # Process other conditionals
    handle_conditional_block "OPENBAO_CACHE_SIZE != \"\"" "OPENBAO_CACHE_SIZE" "$OPENBAO_CACHE_SIZE"
    handle_conditional_block "OPENBAO_HA_ENABLED == \"true\"" "OPENBAO_HA_ENABLED" "$OPENBAO_HA_ENABLED"
    handle_conditional_block "OPENBAO_PLUGIN_DIRECTORY != \"\"" "OPENBAO_PLUGIN_DIRECTORY" "$OPENBAO_PLUGIN_DIRECTORY"
    handle_conditional_block "OPENBAO_METRICS_LISTENER_ENABLED == \"true\"" "OPENBAO_METRICS_LISTENER_ENABLED" "$OPENBAO_METRICS_LISTENER_ENABLED"
    handle_conditional_block "OPENBAO_LICENSE_PATH != \"\"" "OPENBAO_LICENSE_PATH" "$OPENBAO_LICENSE_PATH"
    handle_conditional_block "OPENBAO_ENTROPY_AUGMENTATION == \"true\"" "OPENBAO_ENTROPY_AUGMENTATION" "$OPENBAO_ENTROPY_AUGMENTATION"
    handle_conditional_block "OPENBAO_TELEMETRY_STATSD_ADDRESS != \"\"" "OPENBAO_TELEMETRY_STATSD_ADDRESS" "$OPENBAO_TELEMETRY_STATSD_ADDRESS"
    handle_conditional_block "OPENBAO_TELEMETRY_STATSITE_ADDRESS != \"\"" "OPENBAO_TELEMETRY_STATSITE_ADDRESS" "$OPENBAO_TELEMETRY_STATSITE_ADDRESS"
    
    # Substitute environment variables
    envsubst < "$temp_file" > "$output_file"
    
    # Clean up
    rm -f "$temp_file"
    
    echo "Configuration rendered successfully to: $output_file"
    echo "Configuration preview:"
    echo "====================="
    head -20 "$output_file"
    echo "====================="
}

# Main execution
echo "Starting OpenBao configuration rendering..."

# Ensure required environment variables are set
required_vars=("OPENBAO_API_ADDR" "OPENBAO_CLUSTER_ADDR" "OPENBAO_RAFT_NODE_ID" "OPENBAO_NODE_TYPE")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable $var is not set"
        exit 1
    fi
done

# Create config directory if it doesn't exist
mkdir -p /openbao/config

# Render the configuration
render_template "/openbao/config/openbao.hcl.tpl" "/openbao/config/openbao.hcl"

# Start OpenBao with the rendered configuration
echo "Starting OpenBao server..."
exec openbao server -config=/openbao/config/openbao.hcl
