#!/bin/bash

# Dynamic Docker Compose Generator for OpenBao Cluster
# This script generates a docker-compose.yaml with configurable number of nodes

set -e

# Load environment variables
if [ -f .env ]; then
    source .env
fi

# Configuration
VOTER_NODES=${OPENBAO_VOTER_NODES:-3}
NON_VOTER_NODES=${OPENBAO_NON_VOTER_NODES:-2}
TOTAL_NODES=$((VOTER_NODES + NON_VOTER_NODES))

echo "Generating Docker Compose for OpenBao cluster:"
echo "- Voter nodes: $VOTER_NODES"
echo "- Non-voter nodes: $NON_VOTER_NODES"
echo "- Total nodes: $TOTAL_NODES"

# Generate the docker-compose.yaml
cat > docker-compose.generated.yaml << 'EOF'
version: '3.8'

# YAML Anchors for reusable configurations
x-openbao-common: &openbao-common
  image: openbao/openbao:latest
  cap_add:
    - IPC_LOCK
  expose:
    - "${OPENBAO_API_PORT:-8200}"
    - "${OPENBAO_CLUSTER_PORT:-8201}"
  networks:
    - openbao-cluster
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:${OPENBAO_API_PORT:-8200}/v1/sys/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

x-openbao-environment: &openbao-environment
  OPENBAO_ADDR: http://0.0.0.0:${OPENBAO_API_PORT:-8200}
  OPENBAO_RAFT_PATH: /openbao/data
  OPENBAO_LOG_LEVEL: ${OPENBAO_LOG_LEVEL:-INFO}
  OPENBAO_LOG_FORMAT: ${OPENBAO_LOG_FORMAT:-standard}
  OPENBAO_TELEMETRY_RETENTION: ${OPENBAO_TELEMETRY_RETENTION:-30s}
  OPENBAO_TELEMETRY_DISABLE_HOSTNAME: ${OPENBAO_TELEMETRY_DISABLE_HOSTNAME:-true}
  OPENBAO_TELEMETRY_STATSD_ADDRESS: ${OPENBAO_TELEMETRY_STATSD_ADDRESS:-}
  OPENBAO_TELEMETRY_STATSITE_ADDRESS: ${OPENBAO_TELEMETRY_STATSITE_ADDRESS:-}
  OPENBAO_SEAL_TYPE: ${OPENBAO_SEAL_TYPE:-}
  OPENBAO_SEAL_AWS_REGION: ${OPENBAO_SEAL_AWS_REGION:-}
  OPENBAO_SEAL_AWS_KMS_KEY_ID: ${OPENBAO_SEAL_AWS_KMS_KEY_ID:-}
  OPENBAO_SEAL_GCP_PROJECT: ${OPENBAO_SEAL_GCP_PROJECT:-}
  OPENBAO_SEAL_GCP_REGION: ${OPENBAO_SEAL_GCP_REGION:-}
  OPENBAO_SEAL_GCP_KEY_RING: ${OPENBAO_SEAL_GCP_KEY_RING:-}
  OPENBAO_SEAL_GCP_CRYPTO_KEY: ${OPENBAO_SEAL_GCP_CRYPTO_KEY:-}
  OPENBAO_SEAL_AZURE_TENANT_ID: ${OPENBAO_SEAL_AZURE_TENANT_ID:-}
  OPENBAO_SEAL_AZURE_CLIENT_ID: ${OPENBAO_SEAL_AZURE_CLIENT_ID:-}
  OPENBAO_SEAL_AZURE_CLIENT_SECRET: ${OPENBAO_SEAL_AZURE_CLIENT_SECRET:-}
  OPENBAO_SEAL_AZURE_VAULT_NAME: ${OPENBAO_SEAL_AZURE_VAULT_NAME:-}
  OPENBAO_SEAL_AZURE_KEY_NAME: ${OPENBAO_SEAL_AZURE_KEY_NAME:-}
  OPENBAO_CACHE_SIZE: ${OPENBAO_CACHE_SIZE:-}
  OPENBAO_HA_ENABLED: ${OPENBAO_HA_ENABLED:-false}
  OPENBAO_PLUGIN_DIRECTORY: ${OPENBAO_PLUGIN_DIRECTORY:-}
  OPENBAO_METRICS_LISTENER_ENABLED: ${OPENBAO_METRICS_LISTENER_ENABLED:-false}
  OPENBAO_METRICS_PORT: ${OPENBAO_METRICS_PORT:-8202}
  OPENBAO_LICENSE_PATH: ${OPENBAO_LICENSE_PATH:-}
  OPENBAO_ENTROPY_AUGMENTATION: ${OPENBAO_ENTROPY_AUGMENTATION:-false}

services:
  # Load Balancer (HAProxy)
  openbao-lb:
    image: haproxy:2.8-alpine
    container_name: openbao-lb
    hostname: openbao-lb
    ports:
      - "${OPENBAO_API_PORT:-8200}:${OPENBAO_API_PORT:-8200}"
      - "${HAPROXY_STATS_PORT:-8404}:${HAPROXY_STATS_PORT:-8404}"
    volumes:
      - ./config/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    environment:
      - HAPROXY_STATS_ENABLED=${HAPROXY_STATS_ENABLED:-true}
      - HAPROXY_STATS_USER=${HAPROXY_STATS_USER:-admin}
      - HAPROXY_STATS_PASSWORD=${HAPROXY_STATS_PASSWORD:-admin}
    networks:
      - openbao-cluster
    restart: unless-stopped
    depends_on:
EOF

# Add dependencies for all nodes
for i in $(seq 1 $TOTAL_NODES); do
    echo "      - openbao-node-$i" >> docker-compose.generated.yaml
done

cat >> docker-compose.generated.yaml << 'EOF'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:${HAPROXY_STATS_PORT:-8404}/stats"]
      interval: 30s
      timeout: 10s
      retries: 3

EOF

# Generate voter nodes
for i in $(seq 1 $VOTER_NODES); do
    cat >> docker-compose.generated.yaml << EOF
  # Voter Node $i
  openbao-node-$i:
    <<: *openbao-common
    container_name: openbao-node-$i
    hostname: openbao-node-$i
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-$i:${OPENBAO_API_PORT:-8200}
      OPENBAO_CLUSTER_ADDR: https://openbao-node-$i:${OPENBAO_CLUSTER_PORT:-8201}
      OPENBAO_RAFT_NODE_ID: node-$i
      OPENBAO_NODE_TYPE: voter
    volumes:
      - openbao-node-$i-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
EOF

    # Add dependencies for non-first nodes
    if [ $i -gt 1 ]; then
        echo "    depends_on:" >> docker-compose.generated.yaml
        echo "      - openbao-node-1" >> docker-compose.generated.yaml
    fi
    
    echo "    entrypoint: [\"/openbao/scripts/render-config.sh\"]" >> docker-compose.generated.yaml
    echo "" >> docker-compose.generated.yaml
done

# Generate non-voter nodes
for i in $(seq $((VOTER_NODES + 1)) $TOTAL_NODES); do
    cat >> docker-compose.generated.yaml << EOF
  # Non-Voter Node $i
  openbao-node-$i:
    <<: *openbao-common
    container_name: openbao-node-$i
    hostname: openbao-node-$i
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-$i:${OPENBAO_API_PORT:-8200}
      OPENBAO_CLUSTER_ADDR: https://openbao-node-$i:${OPENBAO_CLUSTER_PORT:-8201}
      OPENBAO_RAFT_NODE_ID: node-$i
      OPENBAO_NODE_TYPE: non-voter
    volumes:
      - openbao-node-$i-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
    depends_on:
EOF

    # Add dependencies on all voter nodes
    for j in $(seq 1 $VOTER_NODES); do
        echo "      - openbao-node-$j" >> docker-compose.generated.yaml
    done
    
    echo "    entrypoint: [\"/openbao/scripts/render-config.sh\"]" >> docker-compose.generated.yaml
    echo "" >> docker-compose.generated.yaml
done

# Add volumes section
echo "volumes:" >> docker-compose.generated.yaml
for i in $(seq 1 $TOTAL_NODES); do
    cat >> docker-compose.generated.yaml << EOF
  openbao-node-$i-data:
    driver: local
EOF
done

# Add networks section
cat >> docker-compose.generated.yaml << 'EOF'

networks:
  openbao-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: ${OPENBAO_NETWORK_SUBNET:-**********/16}
EOF

echo "Generated docker-compose.generated.yaml with $TOTAL_NODES nodes"
echo ""
echo "To use the generated file:"
echo "  mv docker-compose.generated.yaml docker-compose.yaml"
echo ""
echo "Or to use it directly:"
echo "  docker-compose -f docker-compose.generated.yaml up -d"
