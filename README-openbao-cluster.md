# OpenBao Cluster Setup

This Docker Compose configuration deploys a 5-node OpenBao cluster with:
- **3 Voter nodes** (nodes 1, 2, 3) - participate in leader election
- **2 Non-voter nodes** (nodes 4, 5) - replicate data but don't vote in elections

## Architecture

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Node 1        │  │   Node 2        │  │   Node 3        │
│   (Voter)       │  │   (Voter)       │  │   (Voter)       │
│   Port: 8200    │  │   Port: 8210    │  │   Port: 8220    │
│   Cluster: 8201 │  │   Cluster: 8211 │  │   Cluster: 8221 │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
         ┌─────────────────────┼─────────────────────┐
         │                     │                     │
┌─────────────────┐  ┌─────────────────┐
│   Node 4        │  │   Node 5        │
│   (Non-Voter)   │  │   (Non-Voter)   │
│   Port: 8230    │  │   Port: 8240    │
│   Cluster: 8231 │  │   Cluster: 8241 │
└─────────────────┘  └─────────────────┘
```

## Port Mapping

| Node | Type | API Port | Cluster Port |
|------|------|----------|--------------|
| Node 1 | Voter | 8200 | 8201 |
| Node 2 | Voter | 8210 | 8211 |
| Node 3 | Voter | 8220 | 8221 |
| Node 4 | Non-Voter | 8230 | 8231 |
| Node 5 | Non-Voter | 8240 | 8241 |

## Quick Start

1. **Start the cluster:**
   ```bash
   docker-compose up -d
   ```

2. **Check cluster status:**
   ```bash
   docker-compose ps
   ```

3. **Initialize the cluster (only needed once):**
   ```bash
   # Initialize on node 1
   docker exec -it openbao-node-1 openbao operator init
   ```

4. **Unseal all nodes:**
   ```bash
   # Use the unseal keys from initialization
   docker exec -it openbao-node-1 openbao operator unseal <key1>
   docker exec -it openbao-node-1 openbao operator unseal <key2>
   docker exec -it openbao-node-1 openbao operator unseal <key3>
   
   # Repeat for other nodes
   docker exec -it openbao-node-2 openbao operator unseal <key1>
   docker exec -it openbao-node-2 openbao operator unseal <key2>
   docker exec -it openbao-node-2 openbao operator unseal <key3>
   
   # ... and so on for nodes 3, 4, 5
   ```

5. **Access the UI:**
   - Node 1: http://localhost:8200
   - Node 2: http://localhost:8210
   - Node 3: http://localhost:8220
   - Node 4: http://localhost:8230
   - Node 5: http://localhost:8240

## Cluster Management

### Check Raft Peers
```bash
docker exec -it openbao-node-1 openbao operator raft list-peers
```

### Add Non-Voter Nodes (if needed manually)
```bash
# This should happen automatically, but if needed:
docker exec -it openbao-node-1 openbao operator raft join -non-voter http://openbao-node-4:8200
docker exec -it openbao-node-1 openbao operator raft join -non-voter http://openbao-node-5:8200
```

### Monitor Cluster Health
```bash
# Check health of all nodes
for port in 8200 8210 8220 8230 8240; do
  echo "Node on port $port:"
  curl -s http://localhost:$port/v1/sys/health | jq .
done
```

## Logs

View logs for specific nodes:
```bash
docker-compose logs openbao-node-1
docker-compose logs openbao-node-2
# ... etc
```

## Stopping the Cluster

```bash
docker-compose down
```

To also remove volumes (WARNING: This will delete all data):
```bash
docker-compose down -v
```

## Notes

- The cluster uses Raft consensus protocol
- Voter nodes participate in leader election (minimum 2 needed for quorum)
- Non-voter nodes replicate data but don't vote
- All nodes can serve read requests
- Only the leader can serve write requests
- TLS is disabled for simplicity (enable in production)
- Data is persisted in Docker volumes

## Production Considerations

For production use, consider:
1. Enable TLS encryption
2. Use proper secrets management
3. Configure backup strategies
4. Set up monitoring and alerting
5. Use external load balancer
6. Implement proper network security
7. Configure auto-unseal mechanisms
