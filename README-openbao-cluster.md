# OpenBao Cluster Setup with Load Balancer

This Docker Compose configuration deploys a 5-node OpenBao cluster with HAProxy load balancer:
- **3 Voter nodes** (nodes 1, 2, 3) - participate in leader election
- **2 Non-voter nodes** (nodes 4, 5) - replicate data but don't vote in elections
- **HAProxy Load Balancer** - distributes traffic across all nodes

## Architecture

```
                    ┌─────────────────────┐
                    │    Load Balancer    │
                    │      (HAProxy)      │
                    │    Port: 8200       │
                    │    Stats: 8404      │
                    └──────────┬──────────┘
                               │
        ┌──────────────────────┼──────────────────────┐
        │                      │                      │
┌───────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│   Node 1      │    │   Node 2        │    │   Node 3        │
│   (Voter)     │    │   (Voter)       │    │   (Voter)       │
│   Internal    │    │   Internal      │    │   Internal      │
└───────────────┘    └─────────────────┘    └─────────────────┘
        │                      │                      │
        └──────────────────────┼──────────────────────┘
                               │
        ┌──────────────────────┼──────────────────────┐
        │                      │                      │
┌───────▼───────┐    ┌─────────▼───────┐
│   Node 4      │    │   Node 5        │
│   (Non-Voter) │    │   (Non-Voter)   │
│   Internal    │    │   Internal      │
└───────────────┘    └─────────────────┘
```

## Network Configuration

| Service | External Port | Internal Port | Purpose |
|---------|---------------|---------------|---------|
| Load Balancer | 8200 | 8200 | OpenBao API |
| Load Balancer | 8404 | 8404 | HAProxy Stats |
| All Nodes | - | 8200 | Internal API |
| All Nodes | - | 8201 | Internal Cluster |

## Quick Start

1. **Start the cluster:**
   ```bash
   docker-compose up -d
   ```

2. **Check cluster status:**
   ```bash
   docker-compose ps
   ```

3. **Initialize the cluster (only needed once):**
   ```bash
   # Initialize on node 1
   docker exec -it openbao-node-1 openbao operator init
   ```

4. **Unseal all nodes:**
   ```bash
   # Use the unseal keys from initialization
   docker exec -it openbao-node-1 openbao operator unseal <key1>
   docker exec -it openbao-node-1 openbao operator unseal <key2>
   docker exec -it openbao-node-1 openbao operator unseal <key3>
   
   # Repeat for other nodes
   docker exec -it openbao-node-2 openbao operator unseal <key1>
   docker exec -it openbao-node-2 openbao operator unseal <key2>
   docker exec -it openbao-node-2 openbao operator unseal <key3>
   
   # ... and so on for nodes 3, 4, 5
   ```

5. **Access the cluster:**
   - **OpenBao API**: http://localhost:8200 (through load balancer)
   - **HAProxy Stats**: http://localhost:8404/stats
   - All requests are automatically load-balanced across healthy nodes

## Cluster Management

### Check Raft Peers
```bash
docker exec -it openbao-node-1 openbao operator raft list-peers
```

### Add Non-Voter Nodes (if needed manually)
```bash
# This should happen automatically, but if needed:
docker exec -it openbao-node-1 openbao operator raft join -non-voter http://openbao-node-4:8200
docker exec -it openbao-node-1 openbao operator raft join -non-voter http://openbao-node-5:8200
```

### Monitor Cluster Health

```bash
# Check health through load balancer
curl -s http://localhost:8200/v1/sys/health | jq .

# Check load balancer stats
curl -s http://localhost:8404/stats

# Use the management script
./cluster-management.sh health
```

## Logs

View logs for specific nodes:
```bash
docker-compose logs openbao-node-1
docker-compose logs openbao-node-2
# ... etc
```

## Stopping the Cluster

```bash
docker-compose down
```

To also remove volumes (WARNING: This will delete all data):
```bash
docker-compose down -v
```

## Load Balancer Features

- **High Availability**: Automatic failover if nodes become unhealthy
- **Load Distribution**: Round-robin distribution across all healthy nodes
- **Health Monitoring**: Continuous health checks on all backend nodes
- **Statistics**: Real-time monitoring via HAProxy stats interface
- **Flexible Routing**: Can route to all nodes or leader-only for writes

## Notes

- The cluster uses Raft consensus protocol
- Voter nodes participate in leader election (minimum 2 needed for quorum)
- Non-voter nodes replicate data but don't vote
- All nodes can serve read requests through the load balancer
- Write requests are automatically routed to the active leader
- TLS is disabled for simplicity (enable in production)
- Data is persisted in Docker volumes
- All communication happens through internal Docker network

## Production Considerations

For production use, consider:
1. Enable TLS encryption
2. Use proper secrets management
3. Configure backup strategies
4. Set up monitoring and alerting
5. Use external load balancer
6. Implement proper network security
7. Configure auto-unseal mechanisms
