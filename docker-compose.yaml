version: '3.8'

services:
  # Voter Node 1 (Leader candidate)
  openbao-node-1:
    image: openbao/openbao:latest
    container_name: openbao-node-1
    hostname: openbao-node-1
    cap_add:
      - IPC_LOCK
    environment:
      - OPENBAO_ADDR=http://0.0.0.0:8200
      - OPENBAO_API_ADDR=http://openbao-node-1:8200
      - OPENBAO_CLUSTER_ADDR=https://openbao-node-1:8201
      - OPENBAO_RAFT_NODE_ID=node-1
      - OPENBAO_RAFT_PATH=/openbao/data
    ports:
      - "8200:8200"
      - "8201:8201"
    volumes:
      - openbao-node-1-data:/openbao/data
      - ./config/node-1.hcl:/openbao/config/openbao.hcl:ro
    command: ["openbao", "server", "-config=/openbao/config/openbao.hcl"]
    networks:
      - openbao-cluster
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8200/v1/sys/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Voter Node 2
  openbao-node-2:
    image: openbao/openbao:latest
    container_name: openbao-node-2
    hostname: openbao-node-2
    cap_add:
      - IPC_LOCK
    environment:
      - OPENBAO_ADDR=http://0.0.0.0:8200
      - OPENBAO_API_ADDR=http://openbao-node-2:8200
      - OPENBAO_CLUSTER_ADDR=https://openbao-node-2:8201
      - OPENBAO_RAFT_NODE_ID=node-2
      - OPENBAO_RAFT_PATH=/openbao/data
    ports:
      - "8210:8200"
      - "8211:8201"
    volumes:
      - openbao-node-2-data:/openbao/data
      - ./config/node-2.hcl:/openbao/config/openbao.hcl:ro
    command: ["openbao", "server", "-config=/openbao/config/openbao.hcl"]
    networks:
      - openbao-cluster
    restart: unless-stopped
    depends_on:
      - openbao-node-1
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8200/v1/sys/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Voter Node 3
  openbao-node-3:
    image: openbao/openbao:latest
    container_name: openbao-node-3
    hostname: openbao-node-3
    cap_add:
      - IPC_LOCK
    environment:
      - OPENBAO_ADDR=http://0.0.0.0:8200
      - OPENBAO_API_ADDR=http://openbao-node-3:8200
      - OPENBAO_CLUSTER_ADDR=https://openbao-node-3:8201
      - OPENBAO_RAFT_NODE_ID=node-3
      - OPENBAO_RAFT_PATH=/openbao/data
    ports:
      - "8220:8200"
      - "8221:8201"
    volumes:
      - openbao-node-3-data:/openbao/data
      - ./config/node-3.hcl:/openbao/config/openbao.hcl:ro
    command: ["openbao", "server", "-config=/openbao/config/openbao.hcl"]
    networks:
      - openbao-cluster
    restart: unless-stopped
    depends_on:
      - openbao-node-1
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8200/v1/sys/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Non-Voter Node 4
  openbao-node-4:
    image: openbao/openbao:latest
    container_name: openbao-node-4
    hostname: openbao-node-4
    cap_add:
      - IPC_LOCK
    environment:
      - OPENBAO_ADDR=http://0.0.0.0:8200
      - OPENBAO_API_ADDR=http://openbao-node-4:8200
      - OPENBAO_CLUSTER_ADDR=https://openbao-node-4:8201
      - OPENBAO_RAFT_NODE_ID=node-4
      - OPENBAO_RAFT_PATH=/openbao/data
    ports:
      - "8230:8200"
      - "8231:8201"
    volumes:
      - openbao-node-4-data:/openbao/data
      - ./config/node-4.hcl:/openbao/config/openbao.hcl:ro
    command: ["openbao", "server", "-config=/openbao/config/openbao.hcl"]
    networks:
      - openbao-cluster
    restart: unless-stopped
    depends_on:
      - openbao-node-1
      - openbao-node-2
      - openbao-node-3
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8200/v1/sys/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Non-Voter Node 5
  openbao-node-5:
    image: openbao/openbao:latest
    container_name: openbao-node-5
    hostname: openbao-node-5
    cap_add:
      - IPC_LOCK
    environment:
      - OPENBAO_ADDR=http://0.0.0.0:8200
      - OPENBAO_API_ADDR=http://openbao-node-5:8200
      - OPENBAO_CLUSTER_ADDR=https://openbao-node-5:8201
      - OPENBAO_RAFT_NODE_ID=node-5
      - OPENBAO_RAFT_PATH=/openbao/data
    ports:
      - "8240:8200"
      - "8241:8201"
    volumes:
      - openbao-node-5-data:/openbao/data
      - ./config/node-5.hcl:/openbao/config/openbao.hcl:ro
    command: ["openbao", "server", "-config=/openbao/config/openbao.hcl"]
    networks:
      - openbao-cluster
    restart: unless-stopped
    depends_on:
      - openbao-node-1
      - openbao-node-2
      - openbao-node-3
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8200/v1/sys/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  openbao-node-1-data:
    driver: local
  openbao-node-2-data:
    driver: local
  openbao-node-3-data:
    driver: local
  openbao-node-4-data:
    driver: local
  openbao-node-5-data:
    driver: local

networks:
  openbao-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16