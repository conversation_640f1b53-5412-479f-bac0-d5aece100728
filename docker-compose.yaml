version: '3.8'

# YAML Anchors for reusable configurations
x-openbao-common: &openbao-common
  image: openbao/openbao:latest
  cap_add:
    - IPC_LOCK
  expose:
    - "${OPENBAO_API_PORT:-8200}"
    - "${OPENBAO_CLUSTER_PORT:-8201}"
  networks:
    - openbao-cluster
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:${OPENBAO_API_PORT:-8200}/v1/sys/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

x-openbao-environment: &openbao-environment
  OPENBAO_ADDR: http://0.0.0.0:${OPENBAO_API_PORT:-8200}
  OPENBAO_RAFT_PATH: /openbao/data
  OPENBAO_LOG_LEVEL: ${OPENBAO_LOG_LEVEL:-INFO}
  OPENBAO_LOG_FORMAT: ${OPENBAO_LOG_FORMAT:-standard}
  OPENBAO_TELEMETRY_RETENTION: ${OPENBAO_TELEMETRY_RETENTION:-30s}
  OPENBAO_TELEMETRY_DISABLE_HOSTNAME: ${OPENBAO_TELEMETRY_DISABLE_HOSTNAME:-true}
  OPENBAO_TELEMETRY_STATSD_ADDRESS: ${OPENBAO_TELEMETRY_STATSD_ADDRESS:-}
  OPENBAO_TELEMETRY_STATSITE_ADDRESS: ${OPENBAO_TELEMETRY_STATSITE_ADDRESS:-}
  OPENBAO_SEAL_TYPE: ${OPENBAO_SEAL_TYPE:-}
  OPENBAO_SEAL_AWS_REGION: ${OPENBAO_SEAL_AWS_REGION:-}
  OPENBAO_SEAL_AWS_KMS_KEY_ID: ${OPENBAO_SEAL_AWS_KMS_KEY_ID:-}
  OPENBAO_SEAL_GCP_PROJECT: ${OPENBAO_SEAL_GCP_PROJECT:-}
  OPENBAO_SEAL_GCP_REGION: ${OPENBAO_SEAL_GCP_REGION:-}
  OPENBAO_SEAL_GCP_KEY_RING: ${OPENBAO_SEAL_GCP_KEY_RING:-}
  OPENBAO_SEAL_GCP_CRYPTO_KEY: ${OPENBAO_SEAL_GCP_CRYPTO_KEY:-}
  OPENBAO_SEAL_AZURE_TENANT_ID: ${OPENBAO_SEAL_AZURE_TENANT_ID:-}
  OPENBAO_SEAL_AZURE_CLIENT_ID: ${OPENBAO_SEAL_AZURE_CLIENT_ID:-}
  OPENBAO_SEAL_AZURE_CLIENT_SECRET: ${OPENBAO_SEAL_AZURE_CLIENT_SECRET:-}
  OPENBAO_SEAL_AZURE_VAULT_NAME: ${OPENBAO_SEAL_AZURE_VAULT_NAME:-}
  OPENBAO_SEAL_AZURE_KEY_NAME: ${OPENBAO_SEAL_AZURE_KEY_NAME:-}
  OPENBAO_CACHE_SIZE: ${OPENBAO_CACHE_SIZE:-}
  OPENBAO_HA_ENABLED: ${OPENBAO_HA_ENABLED:-false}
  OPENBAO_PLUGIN_DIRECTORY: ${OPENBAO_PLUGIN_DIRECTORY:-}
  OPENBAO_METRICS_LISTENER_ENABLED: ${OPENBAO_METRICS_LISTENER_ENABLED:-false}
  OPENBAO_METRICS_PORT: ${OPENBAO_METRICS_PORT:-8202}
  OPENBAO_LICENSE_PATH: ${OPENBAO_LICENSE_PATH:-}
  OPENBAO_ENTROPY_AUGMENTATION: ${OPENBAO_ENTROPY_AUGMENTATION:-false}

x-voter-dependencies: &voter-dependencies
  - openbao-node-1

x-non-voter-dependencies: &non-voter-dependencies
  - openbao-node-1
  - openbao-node-2
  - openbao-node-3

services:
  # Load Balancer (HAProxy)
  openbao-lb:
    image: haproxy:2.8-alpine
    container_name: openbao-lb
    hostname: openbao-lb
    ports:
      - "${OPENBAO_API_PORT:-8200}:${OPENBAO_API_PORT:-8200}"  # Main API access
      - "${HAPROXY_STATS_PORT:-8404}:${HAPROXY_STATS_PORT:-8404}"  # HAProxy stats
    volumes:
      - ./config/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    environment:
      - HAPROXY_STATS_ENABLED=${HAPROXY_STATS_ENABLED:-true}
      - HAPROXY_STATS_USER=${HAPROXY_STATS_USER:-admin}
      - HAPROXY_STATS_PASSWORD=${HAPROXY_STATS_PASSWORD:-admin}
    networks:
      - openbao-cluster
    restart: unless-stopped
    depends_on:
      - openbao-node-1
      - openbao-node-2
      - openbao-node-3
      - openbao-node-4
      - openbao-node-5
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:${HAPROXY_STATS_PORT:-8404}/stats"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Voter Node 1 (Leader candidate)
  openbao-node-1:
    <<: *openbao-common
    container_name: openbao-node-1
    hostname: openbao-node-1
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-1:8200
      OPENBAO_CLUSTER_ADDR: https://openbao-node-1:8201
      OPENBAO_RAFT_NODE_ID: node-1
      OPENBAO_NODE_TYPE: voter
    volumes:
      - openbao-node-1-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
    entrypoint: ["/openbao/scripts/render-config.sh"]

  # Voter Node 2
  openbao-node-2:
    <<: *openbao-common
    container_name: openbao-node-2
    hostname: openbao-node-2
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-2:8200
      OPENBAO_CLUSTER_ADDR: https://openbao-node-2:8201
      OPENBAO_RAFT_NODE_ID: node-2
      OPENBAO_NODE_TYPE: voter
    volumes:
      - openbao-node-2-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
    depends_on: *voter-dependencies
    entrypoint: ["/openbao/scripts/render-config.sh"]

  # Voter Node 3
  openbao-node-3:
    <<: *openbao-common
    container_name: openbao-node-3
    hostname: openbao-node-3
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-3:8200
      OPENBAO_CLUSTER_ADDR: https://openbao-node-3:8201
      OPENBAO_RAFT_NODE_ID: node-3
      OPENBAO_NODE_TYPE: voter
    volumes:
      - openbao-node-3-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
    depends_on: *voter-dependencies
    entrypoint: ["/openbao/scripts/render-config.sh"]

  # Non-Voter Node 4
  openbao-node-4:
    <<: *openbao-common
    container_name: openbao-node-4
    hostname: openbao-node-4
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-4:8200
      OPENBAO_CLUSTER_ADDR: https://openbao-node-4:8201
      OPENBAO_RAFT_NODE_ID: node-4
      OPENBAO_NODE_TYPE: non-voter
    volumes:
      - openbao-node-4-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
    depends_on: *non-voter-dependencies
    entrypoint: ["/openbao/scripts/render-config.sh"]

  # Non-Voter Node 5
  openbao-node-5:
    <<: *openbao-common
    container_name: openbao-node-5
    hostname: openbao-node-5
    environment:
      <<: *openbao-environment
      OPENBAO_API_ADDR: http://openbao-node-5:8200
      OPENBAO_CLUSTER_ADDR: https://openbao-node-5:8201
      OPENBAO_RAFT_NODE_ID: node-5
      OPENBAO_NODE_TYPE: non-voter
    volumes:
      - openbao-node-5-data:/openbao/data
      - ./config/openbao.hcl.tpl:/openbao/config/openbao.hcl.tpl:ro
      - ./scripts/render-config.sh:/openbao/scripts/render-config.sh:ro
    depends_on: *non-voter-dependencies
    entrypoint: ["/openbao/scripts/render-config.sh"]

volumes:
  openbao-node-1-data:
    driver: local
  openbao-node-2-data:
    driver: local
  openbao-node-3-data:
    driver: local
  openbao-node-4-data:
    driver: local
  openbao-node-5-data:
    driver: local

networks:
  openbao-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: ${OPENBAO_NETWORK_SUBNET:-**********/16}